import 'package:flutter/material.dart';
import 'package:neorevv/src/domain/models/report.dart';

import '../../domain/models/agent.dart';
import '../../domain/models/broker.dart';
import '../../domain/models/info_card.dart';
import '../../domain/models/sales_data.dart';
import '../theme/app_theme.dart';
import 'constants.dart';

final List<InfoCardData> infoCards = [
  InfoCardData(
    title: "Total Brokers",
    value: "168",
    assetImage: '$iconAssetpath/brokers.png',
    iconColor: Colors.blue,
    subtitle: "Last Month",
    additionalInfo: "38",
  ),
  InfoCardData(
    title: "Total Agents",
    value: "2850",
    assetImage: '$iconAssetpath/agents.png',
    iconColor: Colors.blue,
    subtitle: "Last Month",
    additionalInfo: "125",
  ),
  InfoCardData(
    title: "Total Sales",
    value: "20K",
    assetImage: '$iconAssetpath/sales.png',
    iconColor: Colors.blue,
    subtitle: "Year",
    additionalInfo: "2025",
  ),
  InfoCardData(
    title: "Total Revenue",
    value: "\$250K",
    assetImage: '$iconAssetpath/revenue.png',
    iconColor: Colors.blue,
    subtitle: "Year",
    additionalInfo: "2025",
  ),
];

// sample data with hierarchical structure
final agentListJson = [
  Agent(
    name: "Sophia Turner(A1)",
    sales: 22,
    amount: 3400,
    commission: 510,
    contact: "(225) 555-0101",
    email: "<EMAIL>",
    relatedBroker: "Charlotte Anderson B1",
    agents: [
      Agent(
        name: "Agent A1.1",
        sales: 22,
        amount: 3400,
        commission: 510,
        contact: "(225) 555-0101",
        email: "<EMAIL>",
        agents: [],
        color: AppTheme.primaryColor,
        imageUrl: "$imageAssetpath/profile.png",
        joinDate: DateTime.now(),
        state: "California",
        city: "Berkeley",
        level: "Level 1",
        totalDeals: 12,
        earning: 2200,
        status: true,
      ),
      Agent(
        name: "Agent A1.2",
        sales: 22,
        amount: 3400,
        commission: 510,
        contact: "(225) 555-0101",
        email: "<EMAIL>",
        agents: [],
        color: AppTheme.primaryColor,
        imageUrl: "$imageAssetpath/profile.png",
        joinDate: DateTime.now(),
        state: "California",
        city: "Berkeley",
        level: "Level 1",
        totalDeals: 12,
        earning: 2200,
        status: true,
      ),
      Agent(
        name: "Agent A1.3",
        sales: 22,
        amount: 3400,
        commission: 510,
        contact: "(225) 555-0101",
        email: "<EMAIL>",
        agents: [],
        color: AppTheme.primaryColor,
        imageUrl: "$imageAssetpath/profile.png",
        joinDate: DateTime.now(),
        state: "California",
        city: "Berkeley",
        level: "Level 1",
        totalDeals: 12,
        earning: 2200,
        status: true,
      ),
    ],
    color: AppTheme.salesColors[9],
    imageUrl: "$imageAssetpath/profile.png",
    joinDate: DateTime(2025, 6, 28),
    state: "California",
    city: "Berkeley",
    level: "Level 1",
    totalDeals: 12,
    earning: 2200,
    status: true,
  ),
  Agent(
    name: "Liam Johnson(B1)",
    sales: 18,
    amount: 2900,
    commission: 430,
    contact: "(225) 555-0102",
    email: "<EMAIL>",
    relatedBroker: "Charlotte Anderson B1",
    agents: [
      Agent(
        name: "Agent B1.1",
        sales: 22,
        amount: 3400,
        commission: 510,
        contact: "(225) 555-0101",
        email: "<EMAIL>",
        agents: [],
        color: AppTheme.primaryColor,
        imageUrl: "$imageAssetpath/profile.png",
        joinDate: DateTime.now(),
        state: "California",
        city: "Berkeley",
        level: "Level 1",
        totalDeals: 12,
        earning: 2200,
        status: true,
      ),
      Agent(
        name: "Agent B1.2",
        sales: 22,
        amount: 3400,
        commission: 510,
        contact: "(225) 555-0101",
        email: "<EMAIL>",
        agents: [],
        color: AppTheme.primaryColor,
        imageUrl: "$imageAssetpath/profile.png",
        joinDate: DateTime.now(),
        state: "California",
        city: "Berkeley",
        level: "Level 1",
        totalDeals: 12,
        earning: 2200,
        status: true,
      ),
      Agent(
        name: "Agent B1.3",
        sales: 22,
        amount: 3400,
        commission: 510,
        contact: "(225) 555-0101",
        email: "<EMAIL>",
        agents: [],
        color: AppTheme.primaryColor,
        imageUrl: "$imageAssetpath/profile.png",
        joinDate: DateTime.now(),
        state: "California",
        city: "Berkeley",
        level: "Level 1",
        totalDeals: 12,
        earning: 2200,
        status: true,
      ),
    ],
    color: AppTheme.primaryBlueColor,
    imageUrl: "$imageAssetpath/profile.png",
    joinDate: DateTime(2025, 7, 3),
    state: "Colorado",
    city: "Walsenburg",
    level: "Level 1",
    totalDeals: 12,
    earning: 2200,
    status: true,
  ),
  Agent(
    name: "Olivia Smith(C1)",
    sales: 25,
    amount: 4100,
    commission: 620,
    contact: "(225) 555-0103",
    email: "<EMAIL>",
    relatedBroker: "Charlotte Anderson B1",
    agents: [
      Agent(
        name: "Agent C1.1",
        sales: 22,
        amount: 3400,
        commission: 510,
        contact: "(225) 555-0101",
        email: "<EMAIL>",
        agents: [],
        color: AppTheme.primaryColor,
        imageUrl: "$imageAssetpath/profile.png",
        joinDate: DateTime.now(),
        state: "California",
        city: "Berkeley",
        level: "Level 1",
        totalDeals: 12,
        earning: 2200,
        status: true,
      ),
      Agent(
        name: "Agent C1.2",
        sales: 22,
        amount: 3400,
        commission: 510,
        contact: "(225) 555-0101",
        email: "<EMAIL>",
        agents: [],
        color: AppTheme.primaryColor,
        imageUrl: "$imageAssetpath/profile.png",
        joinDate: DateTime.now(),
        state: "California",
        city: "Berkeley",
        level: "Level 1",
        totalDeals: 12,
        earning: 2200,
        status: true,
      ),
      Agent(
        name: "Agent C1.3",
        sales: 22,
        amount: 3400,
        commission: 510,
        contact: "(225) 555-0101",
        email: "<EMAIL>",
        agents: [],
        color: AppTheme.primaryColor,
        imageUrl: "$imageAssetpath/profile.png",
        joinDate: DateTime.now(),
        state: "California",
        city: "Berkeley",
        level: "Level 1",
        totalDeals: 12,
        earning: 2200,
        status: true,
      ),
    ],
    color: AppTheme.salesColors[8],

    imageUrl: "$imageAssetpath/profile.png",
    joinDate: DateTime(2025, 5, 20),
    state: "Georgia",
    city: "La Grange",
    level: "Level 1",
    totalDeals: 12,
    earning: 2200,
    status: true,
  ),
  Agent(
    name: "Clint Barton(D1)",
    sales: 22,
    amount: 3400,
    commission: 510,
    contact: "(225) 555-0101",
    email: "<EMAIL>",
    relatedBroker: "Charlotte Anderson B1",
    agents: [
      Agent(
        name: "Agent D1.1",
        sales: 22,
        amount: 3400,
        commission: 510,
        contact: "(225) 555-0101",
        email: "<EMAIL>",
        agents: [],
        color: AppTheme.salesColors[1],
        imageUrl: "$imageAssetpath/profile.png",
        joinDate: DateTime.now(),
        state: "California",
        city: "Berkeley",
        level: "Level 1",
        totalDeals: 12,
        earning: 2200,
        status: true,
      ),
      Agent(
        name: "Agent D1.2",
        sales: 22,
        amount: 3400,
        commission: 510,
        contact: "(225) 555-0101",
        email: "<EMAIL>",
        agents: [],
        color: AppTheme.salesColors[1],
        imageUrl: "$imageAssetpath/profile.png",
        joinDate: DateTime.now(),
        state: "California",
        city: "Berkeley",
        level: "Level 1",
        totalDeals: 12,
        earning: 2200,
        status: true,
      ),
      Agent(
        name: "Agent D1.3",
        sales: 22,
        amount: 3400,
        commission: 510,
        contact: "(225) 555-0101",
        email: "<EMAIL>",
        agents: [],
        color: AppTheme.primaryColor,
        imageUrl: "$imageAssetpath/profile.png",
        joinDate: DateTime.now(),
        state: "California",
        city: "Berkeley",
        level: "Level 1",
        totalDeals: 12,
        earning: 2200,
        status: true,
      ),
    ],
    color: AppTheme.salesColors[3],

    imageUrl: "$imageAssetpath/profile.png",
    joinDate: DateTime(2025, 6, 10),
    state: "Idaho",
    city: "Caldwell",
    level: "Level 1",
    totalDeals: 12,
    earning: 2200,
    status: true,
  ),
  Agent(
    name: "Tony Stark(E1)",
    sales: 22,
    amount: 3400,
    commission: 510,
    contact: "(225) 555-0101",
    email: "<EMAIL>",
    relatedBroker: "Charlotte Anderson B1",
    agents: [
      Agent(
        name: "Agent E1.1",
        sales: 22,
        amount: 3400,
        commission: 510,
        contact: "(225) 555-0101",
        email: "<EMAIL>",
        agents: [],
        color: AppTheme.salesColors[5],
        imageUrl: "$imageAssetpath/profile.png",
        joinDate: DateTime.now(),
        state: "California",
        city: "Berkeley",
        level: "Level 1",
        totalDeals: 12,
        earning: 2200,
        status: true,
      ),
      Agent(
        name: "Agent E1.2",
        sales: 22,
        amount: 3400,
        commission: 510,
        contact: "(225) 555-0101",
        email: "<EMAIL>",
        agents: [],
        color: AppTheme.salesColors[10],
        imageUrl: "$imageAssetpath/profile.png",
        joinDate: DateTime.now(),
        state: "California",
        city: "Berkeley",
        level: "Level 1",
        totalDeals: 12,
        earning: 2200,
        status: true,
      ),
      Agent(
        name: "Agent E1.3",
        sales: 22,
        amount: 3400,
        commission: 510,
        contact: "(225) 555-0101",
        email: "<EMAIL>",
        agents: [],
        color: AppTheme.salesColors[2],

        imageUrl: "$imageAssetpath/profile.png",
        joinDate: DateTime.now(),
        state: "California",
        city: "Berkeley",
        level: "Level 1",
        totalDeals: 12,
        earning: 2200,
        status: true,
      ),
    ],
    color: AppTheme.salesColors[7],

    imageUrl: "$imageAssetpath/profile.png",
    joinDate: DateTime(2025, 7, 1),
    state: "Indiana",
    city: "New Castle",
    level: "Level 1",
    totalDeals: 12,
    earning: 2200,
    status: false,
  ),
  Agent(
    name: "Jake Williams(F1)",
    sales: 20,
    amount: 3600,
    commission: 540,
    contact: "(225) 555-0104",
    email: "<EMAIL>",
    agents: [],
    color: AppTheme.salesColors[2],

    imageUrl: "$imageAssetpath/profile.png",
    relatedBroker: "Charlotte Anderson B1",
    joinDate: DateTime(2025, 4, 18),
    state: "Iowa",
    city: "Des Moines",
    level: "Level 1",
    totalDeals: 12,
    earning: 2200,
    status: true,
  ),
  Agent(
    name: "Emma Watson(G1)",
    sales: 20,
    amount: 3600,
    commission: 540,
    contact: "(225) 555-0104",
    email: "<EMAIL>",
    agents: [],
    color: AppTheme.salesColors[4],

    imageUrl: "$imageAssetpath/profile.png",
    relatedBroker: "Charlotte Anderson B1",
    joinDate: DateTime(2025, 6, 30),
    state: "Kansas",
    city: "Wichita",
    level: "Level 1",
    totalDeals: 12,
    earning: 2200,
    status: true,
  ),
  Agent(
    name: "Noah Williams(A2)",
    sales: 20,
    amount: 3600,
    commission: 540,
    contact: "(225) 555-0104",
    email: "<EMAIL>",
    agents: [],
    color: AppTheme.primaryColor,
    imageUrl: "$imageAssetpath/profile.png",
    relatedBroker: 'Benjamin Thomas B2',
    joinDate: DateTime(2025, 5, 5),
    state: "Kentucky",
    city: "Louisville",
    level: "Level 1",
    totalDeals: 12,
    earning: 2200,
    status: true,
  ),
  Agent(
    name: "Emma Brown(A2)",
    sales: 16,
    amount: 2700,
    commission: 400,
    contact: "(225) 555-0105",
    email: "<EMAIL>",
    agents: [],
    color: AppTheme.primaryBlueColor,
    imageUrl: "$imageAssetpath/profile.png",
    relatedBroker: 'Benjamin Thomas B2',
    joinDate: DateTime(2025, 5, 12),
    state: "Louisiana",
    city: "Baton Rouge",
    level: "Level 1",
    totalDeals: 12,
    earning: 2200,
    status: false,
  ),
  Agent(
    name: "Mason Davis(A2)",
    sales: 19,
    amount: 3200,
    commission: 480,
    contact: "(225) 555-0106",
    email: "<EMAIL>",
    agents: [],
    color: AppTheme.commissionCardDarkColor,
    imageUrl: "$imageAssetpath/profile.png",
    relatedBroker: 'Benjamin Thomas B2',
    joinDate: DateTime(2025, 7, 6),
    state: "Maine",
    city: "Augusta",
    level: "Level 1",
    totalDeals: 12,
    earning: 2200,
    status: true,
  ),
  Agent(
    name: "Ava Miller(A3)",
    sales: 23,
    amount: 3900,
    commission: 590,
    contact: "(225) 555-0107",
    email: "<EMAIL>",
    agents: [],
    color: AppTheme.salesColors[11],

    imageUrl: "$imageAssetpath/profile.png",
    relatedBroker: 'Mia Jackson B3',
    joinDate: DateTime(2025, 3, 15),
    state: "Maryland",
    city: "Baltimore",
    level: "Level 1",
    totalDeals: 12,
    earning: 2200,
    status: true,
  ),
  Agent(
    name: "Elijah Wilson(A3)",
    sales: 21,
    amount: 3500,
    commission: 530,
    contact: "(225) 555-0108",
    email: "<EMAIL>",
    agents: [],
    color: AppTheme.salesColors[10],

    imageUrl: "$imageAssetpath/profile.png",
    relatedBroker: 'Mia Jackson B3',
    joinDate: DateTime(2025, 4, 10),
    state: "Massachusetts",
    city: "Boston",
    level: "Level 1",
    totalDeals: 12,
    earning: 2200,
    status: true,
  ),
  Agent(
    name: "Isabella Moore(A3)",
    sales: 17,
    amount: 2800,
    commission: 420,
    contact: "(225) 555-0109",
    email: "<EMAIL>",
    agents: [],
    color: AppTheme.salesColors[9],

    imageUrl: "$imageAssetpath/profile.png",
    relatedBroker: 'Mia Jackson B3',
    joinDate: DateTime(2025, 5, 25),
    state: "Michigan",
    city: "Lansing",
    level: "Level 1",
    totalDeals: 12,
    earning: 2200,
    status: true,
  ),
  Agent(
    name: "James Taylor(A4)",
    sales: 15,
    amount: 2500,
    commission: 380,
    contact: "(225) 555-0110",
    email: "<EMAIL>",
    agents: [],
    color: AppTheme.salesColors[8],
    imageUrl: "$imageAssetpath/profile.png",
    relatedBroker: "William White B4",
    joinDate: DateTime(2025, 6, 28),
    state: "California",
    city: "Berkeley",
    level: "Level 1",
    totalDeals: 12,
    earning: 2200,
    status: true,
  ),
  Agent(
    name: "Mia White(A4)",
    sales: 14,
    amount: 2300,
    commission: 350,
    contact: "(225) 555-0111",
    email: "<EMAIL>",
    agents: [],
    color: AppTheme.salesColors[7],

    imageUrl: "$imageAssetpath/profile.png",
    relatedBroker: "William White B4",
    joinDate: DateTime(2025, 7, 3),
    state: "Colorado",
    city: "Walsenburg",
    level: "Level 1",
    totalDeals: 12,
    earning: 2200,
    status: true,
  ),
  Agent(
    name: "Jackson Lee(A4)",
    sales: 13,
    amount: 2100,
    commission: 320,
    contact: "(225) 555-0112",
    email: "<EMAIL>",
    agents: [],
    color: AppTheme.salesColors[6],
    imageUrl: "$imageAssetpath/profile.png",
    relatedBroker: "William White B4",
    joinDate: DateTime(2025, 5, 20),
    state: "Georgia",
    city: "La Grange",
    level: "Level 1",
    totalDeals: 12,
    earning: 2200,
    status: true,
  ),
  Agent(
    name: "Amelia Robinson(A5)",
    sales: 12,
    amount: 2000,
    commission: 300,
    contact: "(225) 555-0113",
    email: "<EMAIL>",
    agents: [],
    color: AppTheme.salesColors[5],
    imageUrl: "$imageAssetpath/profile.png",
    relatedBroker: "Amelia Harris B5",
    joinDate: DateTime(2025, 6, 10),
    state: "Idaho",
    city: "Caldwell",
    level: "Level 1",
    totalDeals: 12,
    earning: 2200,
    status: true,
  ),
  Agent(
    name: "Chris Hemworth(A5)",
    sales: 12,
    amount: 2000,
    commission: 300,
    contact: "(225) 555-0113",
    email: "<EMAIL>",
    agents: [],
    color: AppTheme.salesColors[3],
    imageUrl: "$imageAssetpath/profile.png",
    relatedBroker: "Amelia Harris B5",
    joinDate: DateTime(2025, 7, 1),
    state: "Indiana",
    city: "New Castle",
    level: "Level 1",
    totalDeals: 12,
    earning: 2200,
    status: false,
  ),
  Agent(
    name: "Bruce Banner(A5)",
    sales: 12,
    amount: 2000,
    commission: 300,
    contact: "(225) 555-0113",
    email: "<EMAIL>",
    agents: [],
    color: AppTheme.salesColors[4],
    imageUrl: "$imageAssetpath/profile.png",
    relatedBroker: "Amelia Harris B5",
    joinDate: DateTime(2025, 4, 18),
    state: "Iowa",
    city: "Des Moines",
    level: "Level 1",
    totalDeals: 12,
    earning: 2200,
    status: true,
  ),
  Agent(
    name: "Bruce Banner(A5)",
    sales: 12,
    amount: 2000,
    commission: 300,
    contact: "(225) 555-0113",
    email: "<EMAIL>",
    agents: [],
    color: AppTheme.salesColors[0],
    imageUrl: "$imageAssetpath/profile.png",
    relatedBroker: "Amelia Harris B5",
    joinDate: DateTime(2025, 6, 30),
    state: "Kansas",
    city: "Wichita",
    level: "Level 1",
    totalDeals: 12,
    earning: 2200,
    status: true,
  ),
  Agent(
    name: "Natasha Romanoff(A6)",
    sales: 12,
    amount: 2000,
    commission: 300,
    contact: "(225) 555-0113",
    email: "<EMAIL>",
    agents: [],
    color: AppTheme.salesColors[1],
    imageUrl: "$imageAssetpath/profile.png",
    relatedBroker: "Lucas Martin B6",
    joinDate: DateTime(2025, 5, 5),
    state: "Kentucky",
    city: "Louisville",
    level: "Level 1",
    totalDeals: 12,
    earning: 2200,
    status: true,
  ),
];

///
///
final brokersListJson = [
  Broker(
    id: '1',
    name: "Charlotte Anderson B1",
    sales: 12,
    contact: "(415) 555-0101",
    email: "<EMAIL>",
    agents: [...agentListJson.sublist(0, 8)],
    imageUrl: "$iconAssetpath/agent_round.png",
    totalSalesRevenue: 15000,
    totalCommission: 8000,
    color: AppTheme.salesColors[0],
    joinDate: DateTime(2022, 3, 15),
  ),
  Broker(
    id: '2',
    name: "Benjamin Thomas B2",
    sales: 30,
    contact: "(415) 555-0102",
    email: "<EMAIL>",
    agents: agentListJson.sublist(8, 11),
    imageUrl: "$iconAssetpath/agent_round.png",
    totalSalesRevenue: 32000,
    totalCommission: 12000,
    color: AppTheme.salesColors[1],
    joinDate: DateTime(2021, 8, 22),
  ),
  Broker(
    id: '3',
    name: "Mia Jackson B3",
    sales: 8,
    contact: "(415) 555-0103",
    email: "<EMAIL>",
    agents: agentListJson.sublist(2, 8),
    imageUrl: "$iconAssetpath/agent_round.png",
    totalSalesRevenue: 9000,
    totalCommission: 3500,
    color: AppTheme.salesColors[2],
    joinDate: DateTime(2023, 1, 10),
  ),
  Broker(
    id: '4',
    name: "William White B4",
    sales: 25,
    contact: "(415) 555-0104",
    email: "<EMAIL>",
    agents: agentListJson.sublist(3, 10),
    imageUrl: "$iconAssetpath/agent_round.png",
    totalSalesRevenue: 25000,
    totalCommission: 10000,
    color: AppTheme.salesColors[3],
    joinDate: DateTime(2022, 11, 5),
  ),
  Broker(
    id: '5',
    name: "Amelia Harris B5",
    sales: 13,
    contact: "(415) 555-0105",
    email: "<EMAIL>",
    agents: agentListJson.sublist(0, 6),
    imageUrl: "$iconAssetpath/agent_round.png",
    totalSalesRevenue: 14000,
    totalCommission: 6000,
    color: AppTheme.salesColors[4],
    joinDate: DateTime(2023, 6, 18),
  ),
  Broker(
    id: '6',
    name: "Lucas Martin B6",
    sales: 10,
    contact: "(415) 555-0106",
    email: "<EMAIL>",
    agents: agentListJson.sublist(3, 10),
    imageUrl: "$iconAssetpath/agent_round.png",
    totalSalesRevenue: 11000,
    totalCommission: 4000,
    color: AppTheme.salesColors[5],
    joinDate: DateTime(2022, 7, 30),
  ),
  Broker(
    id: '7',
    name: "Harper Lee B7",
    sales: 30,
    contact: "(415) 555-0107",
    email: "<EMAIL>",
    agents: agentListJson.sublist(0, 10),
    imageUrl: "$iconAssetpath/agent_round.png",
    totalSalesRevenue: 33000,
    totalCommission: 15000,
    color: AppTheme.salesColors[6],
    joinDate: DateTime(2021, 12, 14),
  ),
  Broker(
    id: '8',
    name: "Henry Clark B8",
    sales: 58,
    contact: "(415) 555-0108",
    email: "<EMAIL>",
    agents: agentListJson.sublist(0, 10),
    imageUrl: "$iconAssetpath/agent_round.png",
    totalSalesRevenue: 60000,
    totalCommission: 22000,
    color: AppTheme.salesColors[7],
    joinDate: DateTime(2020, 4, 8),
  ),
  Broker(
    id: '9',
    name: "Evelyn Lewis B9",
    sales: 42,
    contact: "(415) 555-0109",
    email: "<EMAIL>",
    agents: agentListJson.sublist(0, 10),
    imageUrl: "$iconAssetpath/agent_round.png",
    totalSalesRevenue: 42000,
    totalCommission: 17000,
    color: AppTheme.salesColors[8],
    joinDate: DateTime(2021, 9, 25),
  ),
  Broker(
    id: '10',
    name: "Jack Walker B10",
    sales: 30,
    contact: "(415) 555-0110",
    email: "<EMAIL>",
    agents: agentListJson.sublist(0, 10),
    imageUrl: "$iconAssetpath/agent_round.png",
    totalSalesRevenue: 31000,
    totalCommission: 12000,
    color: AppTheme.salesColors[9],
    joinDate: DateTime(2023, 2, 12),
  ),
  Broker(
    id: '11',
    name: "Ella Hall B11",
    sales: 35,
    contact: "(415) 555-0111",
    email: "<EMAIL>",
    agents: agentListJson.sublist(0, 10),
    imageUrl: "$iconAssetpath/agent_round.png",
    totalSalesRevenue: 35000,
    totalCommission: 14000,
    color: AppTheme.salesColors[10],
    joinDate: DateTime(2022, 5, 20),
  ),
  Broker(
    id: '12',
    name: "Alexander Young B12",
    sales: 42,
    contact: "(415) 555-0112",
    email: "<EMAIL>",
    agents: agentListJson.sublist(0, 10),
    imageUrl: "$iconAssetpath/agent_round.png",
    totalSalesRevenue: 4400,
    totalCommission: 1800,
    color: AppTheme.salesColors[4],
    joinDate: DateTime(2021, 10, 3),
  ),
  Broker(
    id: '13',
    name: "Alexander Young B13",
    sales: 42,
    contact: "(415) 555-0112",
    email: "<EMAIL>",
    agents: agentListJson.sublist(0, 10),
    imageUrl: "$iconAssetpath/agent_round.png",
    totalSalesRevenue: 4400,
    totalCommission: 1800,
    color: AppTheme.salesColors[3],
    joinDate: DateTime(2021, 10, 3),
  ),
  Broker(
    id: '14',
    name: "Alexander Young B14",
    sales: 42,
    contact: "(415) 555-0112",
    email: "<EMAIL>",
    agents: agentListJson.sublist(0, 10),
    imageUrl: "$iconAssetpath/agent_round.png",
    totalSalesRevenue: 44000,
    totalCommission: 18000,
    color: AppTheme.salesColors[1],
    joinDate: DateTime(2021, 10, 3),
  ),
  Broker(
    id: '15',
    name: "Alexander Young B15",
    sales: 42,
    contact: "(415) 555-0112",
    email: "<EMAIL>",
    agents: agentListJson.sublist(0, 10),
    imageUrl: "$iconAssetpath/agent_round.png",
    totalSalesRevenue: 4000,
    totalCommission: 1800,
    color: AppTheme.salesColors[11],
    joinDate: DateTime(2021, 10, 3),
  ),
  Broker(
    id: '16',
    name: "Alexander Young B16",
    sales: 42,
    contact: "(415) 555-0112",
    email: "<EMAIL>",
    agents: agentListJson.sublist(0, 10),
    imageUrl: "$iconAssetpath/agent_round.png",
    totalSalesRevenue: 5000,
    totalCommission: 1800,
    color: AppTheme.salesColors[5],
    joinDate: DateTime(2021, 10, 3),
  ),
  Broker(
    id: '17',
    name: "Alexander Young B17",
    sales: 42,
    contact: "(415) 555-0112",
    email: "<EMAIL>",
    agents: agentListJson.sublist(0, 10),
    imageUrl: "$iconAssetpath/agent_round.png",
    totalSalesRevenue: 4000,
    totalCommission: 1800,
    color: AppTheme.salesColors[11],
    joinDate: DateTime(2021, 10, 3),
  ),
  Broker(
    id: '18',
    name: "Alexander Young B18",
    sales: 42,
    contact: "(415) 555-0112",
    email: "<EMAIL>",
    agents: agentListJson.sublist(0, 10),
    imageUrl: "$iconAssetpath/agent_round.png",
    totalSalesRevenue: 4000,
    totalCommission: 1000,
    color: AppTheme.salesColors[6],
    joinDate: DateTime(2021, 10, 3),
  ),
  Broker(
    id: '19',
    name: "Broker19",
    sales: 42,
    contact: "(415) 555-0112",
    email: "<EMAIL>",
    agents: agentListJson.sublist(0, 10),
    imageUrl: "$iconAssetpath/agent_round.png",
    totalSalesRevenue: 2000,
    totalCommission: 1800,
    color: AppTheme.salesColors[1],
    joinDate: DateTime(2021, 10, 3),
  ),
  Broker(
    id: '20',
    name: "Broker20",
    sales: 42,
    contact: "(415) 555-0112",
    email: "<EMAIL>",
    agents: agentListJson.sublist(0, 10),
    imageUrl: "$iconAssetpath/agent_round.png",
    totalSalesRevenue: 10000,
    totalCommission: 1800,
    color: AppTheme.salesColors[0],
    joinDate: DateTime(2021, 10, 3),
  ),
];

final List<SalesData> salesData = [
  SalesData(name: 'Jessica Miller', sales: 30, color: Color(0xFF8B1538)),
  SalesData(name: 'David Thompson', sales: 28, color: Color(0xFFFF9500)),
  SalesData(name: 'Ashley Robinson', sales: 25, color: Color(0xFF4A7C59)),
  SalesData(name: 'Michael Carter', sales: 20, color: Color(0xFF007AFF)),
  SalesData(name: 'Brittany Howard', sales: 18, color: Color(0xFF5AC8FA)),
  SalesData(name: 'James Parker', sales: 15, color: Color(0xFFFFCC02)),
  SalesData(name: 'Emily Johnson', sales: 14, color: Color(0xFF1D1D1F)),
  SalesData(name: 'Christopher Adams', sales: 12, color: Color(0xFFE91E63)),
  SalesData(name: 'Lauren Mitchell', sales: 8, color: Color(0xFF9013FE)),
  SalesData(name: 'Brian Sanders', sales: 5, color: Color(0xFF536DFE)),
];

final List<ReportModel> reportsListData = [
  ReportModel(
    id: '1',
    title: 'Brokerage Sales Volume Report',
    icon: Icons.bar_chart,
    url: "https://mozilla.github.io/pdf.js/web/compressed.tracemonkey-pldi-09.pdf",
    isSelected: true,
  ),
  ReportModel(
    id: '2',
    title: 'Commission Distribution Report',
    icon: Icons.pie_chart,
    url: "https://mozilla.github.io/pdf.js/web/compressed.tracemonkey-pldi-09.pdf",
    isSelected: false,
  ),
  ReportModel(
    id: '3',
    title: 'Transaction Status Report',
    icon: Icons.list_alt,
    url: "https://www.orimi.com/pdf-test.pdf",
    isSelected: false,
  ),
  ReportModel(
    id: '4',
    title: 'Listing Activity Report',
    icon: Icons.home_work,
    url: "https://pdfobject.com/pdf/sample.pdf",
    isSelected: false,
  ),
  ReportModel(
    id: '5',
    title: 'Escrow and Deposits Report',
    icon: Icons.account_balance,
    url: "",
    isSelected: false,
  ),
  ReportModel(
    id: '6',
    title: 'Closing Timeline Report',
    icon: Icons.timeline,
    url: "",
    isSelected: false,
  ),
  ReportModel(
    id: '7',
    title: 'Lead Source Effectiveness Report',
    icon: Icons.trending_up,
    url: "",
    isSelected: false,
  ),
  ReportModel(
    id: '8',
    title: 'Legal & Compliance Report',
    icon: Icons.gavel,
    url: "",
    isSelected: false,
  ),
  ReportModel(
    id: '9',
    title: 'Top Producers Report',
    icon: Icons.star,
    url: "",
    isSelected: false,
  ),
];