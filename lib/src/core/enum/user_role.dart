enum UserRole {
  platformOwner,
  admin,
  broker,
  agent,
  officeStaff,
  receptionist,
  none,
}

// Helper to get display string
String userRoleToString(UserRole role) {
  switch (role) {
    case UserRole.platformOwner:
      return "PLATFORM_OWNER";
    case UserRole.admin:
      return "PLATFORM_ADMIN";
    case UserRole.broker:
      return "BROKER";
    case UserRole.agent:
      return "AGENT";
    case UserRole.officeStaff:
      return "OFFICE_STAFF";
    case UserRole.receptionist:
      return "RECEPTIONIST";
    case UserRole.none:
      return "NONE";
  }
}

UserRole stringToUserRole(String role) {
  switch (role) {
    case "PLATFORM_OWNER":
      return UserRole.platformOwner;
    case "PLATFORM_ADMIN":
      return UserRole.admin;
    case "BROKER":
      return UserRole.broker;
    case "AGENT":
      return UserRole.agent;
    case "OFFICE_STAFF":
      return UserRole.officeStaff;
    case "RECEPTIONIST":
      return UserRole.receptionist;
    case "NONE":
      return UserRole.none;
    default:
      return UserRole.none;
  }
}
