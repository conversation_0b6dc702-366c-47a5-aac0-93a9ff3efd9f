import 'package:flutter/material.dart';

class ReportModel {
  final String id;
  final String title;
  final IconData icon;
  final String url;
  final bool isSelected;

  ReportModel({
    required this.id,
    required this.title,
    required this.icon,
    required this.url,
    required this.isSelected,
  });

  ReportModel copyWith({
    String? id,
    String? title,
    IconData? icon,
    String? url,
    bool? isSelected,
  }) {
    return ReportModel(
      id: id ?? this.id,
      title: title ?? this.title,
      icon: icon ?? this.icon,
      url: url ?? this.url,
      isSelected: isSelected ?? this.isSelected,
    );
  }
}
