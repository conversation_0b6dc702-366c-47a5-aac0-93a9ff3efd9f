import 'dart:ui';

import 'agent.dart';

class Broker {
  String id;
  String name;
  int sales;
  double totalSalesRevenue;
  double totalCommission;
  String imageUrl;
  String contact;
  String email;
  List<Agent> agents;
  Color color;
  DateTime joinDate;

  Broker({
    required this.id,
    required this.name,
    required this.sales,
    required this.imageUrl,
    required this.contact,
    required this.email,
    required this.agents,
    required this.totalSalesRevenue,
    required this.totalCommission,
    required this.color,
    required this.joinDate, // Add joinDate field
  });

  factory Broker.fromJson(Map<String, dynamic> json) {
    return Broker(
      id: json['id'] as String,
      name: json['name'] as String,
      sales: json['sales'] as int,
      imageUrl: json['imageUrl'] as String,
      contact: json['contact'] as String,
      email: json['email'] as String,
      agents: json['agents'] as List<Agent>,
      totalSalesRevenue: json['amount'] as double,
      totalCommission: json['commission'] as double,
      color: json['color'] as Color,
      joinDate: DateTime.parse(
        json['joinDate'] as String,
      ), // Parse joinDate from JSON string to DateTime object
    );
  }
}
