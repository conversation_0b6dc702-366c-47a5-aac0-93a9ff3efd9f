import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import '../../shared/components/elevated_button.dart';
import '../../../core/config/app_strings.dart';
import '../dashboard/components/dashboard_content.dart';
import '../../../core/utils/dotted_line_painter.dart';
import '../../shared/components/breadcrumb_navigation.dart';
import '../../shared/components/radio_btn.dart';
import '../../../core/config/app_strings.dart' as AppStrings;
import '../../../core/config/constants.dart';
import '../../../core/config/responsive.dart';
import '../../../core/theme/app_fonts.dart';
import '../../../core/theme/app_theme.dart';
import 'package:signature/signature.dart';

import '../dashboard/components/header.dart';

class SalesReviewDocScreen extends HookWidget {
  final bool enableEditing;

  SalesReviewDocScreen({Key? key, this.enableEditing = true}) : super(key: key);

  final selectedIndex = ValueNotifier(0);
  final isEditable = ValueNotifier(false);

  @override
  Widget build(BuildContext context) {
    final isMobile = Responsive.isMobile(context);
    final header = Header(selectedTab: '');

    final SignatureController _controller = useMemoized(
      () => SignatureController(
        penStrokeWidth: 2,
        penColor: Colors.black,
        exportBackgroundColor: Colors.white,
      ),
    );
    final isBuyer = useState<bool?>(true);
    final saleType = useState<int>(0);

    useEffect(() {
      isEditable.value = enableEditing;
      return () => _controller.dispose();
    }, [_controller]);

    return ValueListenableBuilder(
      valueListenable: isEditable,
      builder: (context, value, child) {
        return _formWidget(context, isMobile, isBuyer, saleType, _controller);
      },
    );
  }

  Widget _formWidget(
    BuildContext context,
    bool isMobile,
    ValueNotifier<bool?> isBuyer,
    ValueNotifier<int> saleType,
    SignatureController _controller,
  ) {
    final size = MediaQuery.of(context).size;
    final isTab = Responsive.isTablet(context);
    return ValueListenableBuilder(
      valueListenable: selectedIndex,
      builder: (context, value, child) {
        return Container(
          width: double.infinity,
          decoration: BoxDecoration(
            image: DecorationImage(
              fit: BoxFit.cover,
              image: AssetImage('$imageAssetpath/register_bg.png'),
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // BreadCrumbNavigation(
              //   hierarchyPath: [
              //     AppStrings.dashboardAdmin,
              //     AppStrings.reviewClosingDocument,
              //     AppStrings.editClosingDocument,
              //   ],
              //   onNavigate: (int navigationIndex) {},
              // ),
              // SizedBox(height: defaultPadding),
              Container(
                decoration: BoxDecoration(
                  color: AppTheme.roundIconColor,
                  borderRadius: BorderRadius.circular(25),
                ),
                margin: EdgeInsets.symmetric(
                  horizontal: isMobile
                      ? defaultPadding * 2.5
                      : isTab
                      ? defaultPadding * 3
                      : size.width / 4,
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Header with title and buttons
                    _formHeader(context, isBuyer),
                    // Main form content
                    _formContent(
                      context,
                      isMobile,
                      isBuyer,
                      saleType,
                      _controller,
                    ),
                  ],
                ),
              ),
              SizedBox(
                height: selectedIndex.value == 0 ? defaultPadding * 2 : 0,
              ),
              // const Footer(),
            ],
          ),
        );
      },
    );
  }

  Container _formHeader(BuildContext context, ValueNotifier<bool?> isBuyer) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(
        horizontal: defaultPadding * 2,
        vertical: defaultPadding * 2,
      ),
      alignment: Alignment.center,
      child: Stack(
        alignment: Alignment.center,
        children: [
          SizedBox(
            width: double.infinity,
            child: Text(
              isEditable.value
                  ? editClosingDocument
                  : AppStrings.closingDocument,
              textAlign: TextAlign.center,
              style: AppFonts.mediumTextStyle(28, color: Colors.white),
            ),
          ),

          if (!isEditable.value)
            Positioned(
              right: 0,
              child: AppButton(
                label: edit,

                backgroundColor: AppTheme.scaffoldBgColor,
                onPressed: () => isEditable.value = !isEditable.value,
                child: Row(
                  children: [
                    Image.asset(
                      '$iconAssetpath/edit.png',
                      height: 14,
                      width: 14,
                    ),
                    const SizedBox(width: 8),
                    Text(edit, style: AppFonts.mediumTextStyle(13)),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _formContent(
    BuildContext context,
    bool isMobile,
    ValueNotifier<bool?> isBuyer,
    ValueNotifier<int> saleType,
    SignatureController _controller,
  ) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(24),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 24,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: LayoutBuilder(
        builder: (context, constraints) {
          return Column(
            children: [
              Padding(
                padding: EdgeInsets.symmetric(vertical: defaultPadding),
                child: Column(
                  children: [
                    _docRepresentingRadio(isBuyer),
                    const SizedBox(height: defaultPadding),
                    // Form fields in two columns
                    Column(
                      children: [
                        // Row 1
                        Container(
                          margin: const EdgeInsets.only(bottom: defaultPadding),
                          padding: _formContainerPadding(),
                          decoration: BoxDecoration(
                            color: AppTheme.formSubsectionBgColor,
                            // borderRadius: BorderRadius.circular(12),
                          ),
                          child: isMobile
                              ? _transactionInfoColumn()
                              : _transationInfoRow(
                                  transactionId,
                                  "form_transaction_id.png",
                                  "TXN-2025-00123",
                                  transactionName,
                                  'represented_contact.png',
                                  "Smith Purchase - 123 Main St",
                                ),
                        ),
                        // Row 2
                        Container(
                          margin: const EdgeInsets.only(bottom: defaultPadding),
                          padding: _formContainerPadding(),
                          child: isMobile
                              ? Column(
                                  children: [
                                    buildTextField(
                                      address,
                                      'form_address_book.png',
                                      "Brittany Hall, 55 East 10th Street, New York, NY 10003",
                                    ),
                                    buildTextField(
                                      salesVolume,
                                      'dollar.png',
                                      "\$500,000",
                                    ),
                                  ],
                                )
                              : _transationInfoRow(
                                  address,
                                  'form_address_book.png',
                                  "Brittany Hall, 55 East 10th Street, New York, NY 10003",
                                  salesVolume,
                                  'dollar.png',
                                  "\$500,000",
                                ),
                        ),
                        // Row 3
                        Container(
                          margin: const EdgeInsets.only(bottom: defaultPadding),
                          padding: _formContainerPadding(),
                          decoration: BoxDecoration(
                            color: AppTheme.formSubsectionBgColor,
                            // borderRadius: BorderRadius.circular(12),
                          ),
                          child: isMobile
                              ? Column(
                                  children: [
                                    buildTextField(
                                      moneyReceived,
                                      'dollar.png',
                                      "\$10,000",
                                    ),
                                    buildTextField(
                                      dateDeposited,
                                      'form_calendar.png',
                                      "07/01/2025",
                                    ),
                                  ],
                                )
                              : _transationInfoRow(
                                  moneyReceived,
                                  'dollar.png',
                                  "\$10,000",
                                  dateDeposited,
                                  'form_calendar.png',
                                  "07/01/2025",
                                ),
                        ),
                        // Sale Type
                        _saleTypeSelection(saleType, isMobile),
                        SizedBox(height: defaultPadding),
                        // Row 4
                        Container(
                          padding: _formContainerPadding(),
                          decoration: BoxDecoration(
                            color: AppTheme.formSubsectionBgColor,
                            // borderRadius: BorderRadius.circular(12),
                          ),
                          child: isMobile
                              ? Column(
                                  children: [
                                    buildTextField(
                                      grossCommission,
                                      'dollar.png',
                                      "\$15,000",
                                    ),
                                    const SizedBox(height: defaultPadding),
                                    buildTextField(
                                      commissionSplit,
                                      'dollar.png',

                                      "\$7,500",
                                    ),
                                  ],
                                )
                              : _transationInfoRow(
                                  moneyReceived,
                                  'dollar.png',
                                  "\$10,000",
                                  commissionSplit,
                                  'dollar.png',
                                  "\$7,500",
                                ),
                        ),
                        SizedBox(height: defaultPadding),
                        // Row 5
                        Container(
                          padding: _formContainerPadding(),
                          child: isMobile
                              ? Column(
                                  children: [
                                    buildTextField(
                                      dateReleased,
                                      'form_calendar.png',
                                      "07/01/2025",
                                    ),
                                    const SizedBox(height: defaultPadding),
                                    buildTextField(
                                      listingDate,
                                      'form_calendar.png',

                                      "06/01/2025",
                                    ),
                                  ],
                                )
                              : _transationInfoRow(
                                  dateReleased,
                                  'form_calendar.png',
                                  "07/01/2025",
                                  listingDate,
                                  'form_calendar.png',
                                  "06/01/2025",
                                ),
                        ),
                        // Row 6
                        Container(
                          padding: _formContainerPadding(),
                          decoration: BoxDecoration(
                            color: AppTheme.formSubsectionBgColor,
                            // borderRadius: BorderRadius.circular(12),
                          ),
                          child: isMobile
                              ? Column(
                                  children: [
                                    buildTextField(
                                      expirationDate,
                                      'form_calendar.png',
                                      "09/01/2025",
                                    ),
                                    const SizedBox(height: defaultPadding),
                                    buildTextField(
                                      closingDate,
                                      'form_calendar.png',
                                      "07/25/2025",
                                    ),
                                  ],
                                )
                              : _transationInfoRow(
                                  expirationDate,
                                  'form_calendar.png',
                                  "09/01/2025",
                                  closingDate,
                                  'form_calendar.png',
                                  "07/25/2025",
                                ),
                        ),
                        SizedBox(height: defaultPadding),
                        // Row 7
                        Container(
                          padding: _formContainerPadding(),
                          child: isMobile
                              ? Column(
                                  children: [
                                    buildTextField(
                                      legalDescription,
                                      'legal_desc.png',
                                      "Lot 12, Block 4, Oakwood Estates, Phoenix",
                                    ),
                                    const SizedBox(height: defaultPadding),
                                    buildTextField(
                                      escrowNumber,
                                      'form_escrow_no.png',
                                      "ESC-20250701-4567",
                                    ),
                                  ],
                                )
                              : _transationInfoRow(
                                  legalDescription,
                                  'legal_desc.png',
                                  "Lot 12, Block 4, Oakwood Estates, Phoenix",
                                  escrowNumber,
                                  'form_escrow_no.png',
                                  "ESC-20250701-4567",
                                ),
                        ),
                        // Row 8
                        Container(
                          margin: const EdgeInsets.only(bottom: defaultPadding),
                          padding: _formContainerPadding(),
                          decoration: BoxDecoration(
                            color: AppTheme.formSubsectionBgColor,
                            // borderRadius: BorderRadius.circular(12),
                          ),
                          child: isMobile
                              ? Column(
                                  children: [
                                    buildTextField(
                                      dateReceived,
                                      'form_calendar.png',
                                      "07/01/2025",
                                    ),
                                    const SizedBox(height: defaultPadding),
                                    buildTextField(
                                      amountReleased,
                                      'dollar.png',
                                      "\$5,000",
                                    ),
                                  ],
                                )
                              : _transationInfoRow(
                                  dateReceived,
                                  'form_calendar.png',

                                  "07/01/2025",
                                  amountReleased,
                                  'dollar.png',
                                  "\$5,000",
                                ),
                        ),
                        // Row 9
                        Align(
                          alignment: Alignment.centerLeft,
                          child: Container(
                            width: isMobile
                                ? double.infinity
                                : (constraints.maxWidth -
                                          (defaultPadding * 5)) /
                                      2,
                            margin: const EdgeInsets.only(
                              bottom: defaultPadding,
                              left: defaultPadding * 2,
                            ),
                            padding: const EdgeInsets.symmetric(vertical: 12),
                            child: buildTextField(
                              representedContact,
                              'represented_contact.png',
                              "John Smith",
                            ),
                          ),
                        ),
                        // Row 10
                        Container(
                          margin: const EdgeInsets.only(bottom: defaultPadding),
                          padding: _formContainerPadding(),
                          decoration: BoxDecoration(
                            color: AppTheme.formSubsectionBgColor,
                            // borderRadius: BorderRadius.circular(12),
                          ),
                          child: isMobile
                              ? Column(
                                  children: [
                                    buildTextField(
                                      firstName,
                                      'form_user.png',
                                      "John",
                                    ),

                                    const SizedBox(height: defaultPadding),
                                    buildTextField(
                                      lastName,
                                      'form_user.png',
                                      "Smith",
                                    ),
                                  ],
                                )
                              : _transationInfoRow(
                                  firstName,
                                  'form_user.png',
                                  "John",
                                  lastName,
                                  'form_user.png',
                                  "Smith",
                                ),
                        ),
                        // Row 11
                        Container(
                          margin: const EdgeInsets.only(bottom: defaultPadding),
                          padding: _formContainerPadding(),
                          child: isMobile
                              ? Column(
                                  children: [
                                    buildTextField(
                                      email,
                                      'mail.png',
                                      "<EMAIL>",
                                    ),
                                    const SizedBox(height: defaultPadding),

                                    buildTextField(
                                      companyPhoneNumber,
                                      'form_phone.png',
                                      "(*************",
                                    ),
                                  ],
                                )
                              : _transationInfoRow(
                                  email,
                                  'mail.png',
                                  "<EMAIL>",
                                  companyPhoneNumber,
                                  'form_phone.png',
                                  "(*************",
                                ),
                        ),
                        // Row 12
                        Container(
                          margin: const EdgeInsets.only(bottom: defaultPadding),
                          padding: _formContainerPadding(),
                          decoration: BoxDecoration(
                            color: AppTheme.formSubsectionBgColor,
                            // borderRadius: BorderRadius.circular(12),
                          ),
                          child: isMobile
                              ? Column(
                                  children: [
                                    buildTextField(
                                      contactAddress,
                                      'form_address_book.png',
                                      "789 Pine Ave, Scottsdale, AZ 85251",
                                    ),
                                    const SizedBox(height: defaultPadding),
                                    buildTextField(
                                      leadSource,
                                      'referral.png',
                                      "Referral",
                                    ),
                                  ],
                                )
                              : _transationInfoRow(
                                  contactAddress,
                                  'form_address_book.png',
                                  "789 Pine Ave, Scottsdale, AZ 85251",
                                  leadSource,
                                  'referral.png',
                                  "Referral",
                                ),
                        ),
                        // Row 13
                        Container(
                          margin: const EdgeInsets.only(bottom: defaultPadding),
                          padding: _formContainerPadding(),
                          child: isMobile
                              ? Column(
                                  children: [
                                    // Sales Agreement
                                    buildTextFieldWithButtons(
                                      AppStrings.salesAgreement,
                                      'doc.png',
                                      AppStrings.documentFileName,
                                    ),
                                    const SizedBox(height: defaultPadding),
                                    // Closing Document
                                    buildTextFieldWithButtons(
                                      AppStrings.closingDocument,
                                      'doc.png',
                                      AppStrings.documentFileName,
                                    ),
                                  ],
                                )
                              : _documentRowWidget(),
                        ),
                      ],
                    ),

                    // Signature and Upload with 'or' text and dotted border for upload
                    if (isEditable.value) ...[
                      _signatureWidget(_controller),
                      SizedBox(height: defaultPadding * 3),
                      // Buttons
                      _formFooterActionBtns(),
                      const SizedBox(height: defaultPadding),
                    ],
                  ],
                ),
              ),
              // Footer
            ],
          );
        },
      ),
    );
  }

  EdgeInsets _formContainerPadding() {
    return const EdgeInsets.symmetric(
      vertical: defaultPadding / 2,
      horizontal: defaultPadding * 1.5,
    );
  }

  Widget _documentRowWidget() {
    return Row(
      children: [
        Expanded(
          child: buildTextFieldWithButtons(
            AppStrings.salesAgreement,
            'doc.png',
            AppStrings.documentFileName,
          ),
        ),
        const SizedBox(width: 24),
        Expanded(
          child: buildTextFieldWithButtons(
            AppStrings.closingDocument,
            'doc.png',
            AppStrings.documentFileName,
          ),
        ),
      ],
    );
  }

  Widget _saleTypeSelection(ValueNotifier<int> saleType, bool isMobile) {
    final saleTypes = [
      {'label': traditional, 'value': 0},
      {'label': lease, 'value': 1},
      {'label': commercial, 'value': 2},
    ];

    return Container(
      child: LayoutBuilder(
        builder: (context, constraints) {
          final List<Widget> radioWidgets = [
            Text(
              AppStrings.saleTypeLabel,
              style: AppFonts.regularTextStyle(
                14,
                color: AppTheme.primaryTextColor,
              ),
            ),
          ];
          radioWidgets.addAll(
            saleTypes.map((item) {
              return RadioOption(
                value: item['value']! as int,
                groupValue: saleType.value,
                label: item['label']!.toString(),
                onChanged: (value) => saleType.value = value!,
                enabled: isEditable.value,
              );
            }).toList(),
          );

          return Container(
            padding: const EdgeInsets.symmetric(horizontal: defaultPadding),
            child: isMobile
                ? Wrap(
                    spacing: defaultPadding,
                    runSpacing: defaultPadding,
                    children: radioWidgets,
                  )
                : Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: radioWidgets,
                  ),
          );
        },
      ),
    );
  }

  Widget _transationInfoRow(
    String fieldLeft,
    String iconLeft,
    String valueLeft,
    String fieldRight,
    String iconRight,
    String valueRight,
  ) {
    return Row(
      children: [
        Expanded(child: buildTextField(fieldLeft, iconLeft, valueLeft)),
        const SizedBox(width: 24),
        Expanded(child: buildTextField(fieldRight, iconRight, valueRight)),
      ],
    );
  }

  Widget _transactionInfoColumn() {
    return Column(
      children: [
        buildTextField(
          transactionId,
          "form_transaction_id.png",
          "TXN-2025-00123",
        ),
        buildTextField(
          transactionName,
          'represented_contact.png',
          "Smith Purchase - 123 Main St",
        ),
      ],
    );
  }

  Widget _formFooterActionBtns() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        ElevatedButton(
          onPressed: () => isEditable.value = !isEditable.value,
          style: ElevatedButton.styleFrom(
            backgroundColor: AppTheme.cancelBgColor,
            minimumSize: const Size(120, 44),
          ),
          child: Text(
            AppStrings.cancel,
            style: TextStyle(color: Colors.black87),
          ),
        ),
        const SizedBox(width: 24),
        ElevatedButton(
          onPressed: () => isEditable.value = !isEditable.value,
          style: ElevatedButton.styleFrom(
            backgroundColor: AppTheme.primaryBlueColor,
            minimumSize: const Size(120, 44),
          ),
          child: Text(AppStrings.submit, style: TextStyle(color: Colors.white)),
        ),
      ],
    );
  }

  Builder _signatureWidget(SignatureController _controller) {
    return Builder(
      builder: (context) {
        final isMobile = Responsive.isMobile(context);

        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: defaultPadding * 1.5),
          child: Column(
            children: [
              Align(
                alignment: Alignment.centerLeft,
                child: Text(
                  uploadBrokerSignature,
                  style: AppFonts.regularTextStyle(
                    14,
                    color: AppTheme.primaryTextColor,
                  ),
                ),
              ),
              SizedBox(height: defaultPadding / 2),
              isMobile
                  ? Column(
                      children: [
                        _signatureDrawingPad(context, _controller),
                        SizedBox(height: defaultPadding / 2),
                        _signatureUploadedWidget(context),
                      ],
                    )
                  : Row(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Expanded(
                          child: _signatureDrawingPad(context, _controller),
                        ),
                        SizedBox(width: 8),
                        // OR text
                        Text(
                          AppStrings.or,
                          style: AppFonts.regularTextStyle(
                            14,
                            color: AppTheme.primaryTextColor,
                          ),
                        ),
                        SizedBox(width: 8),
                        Expanded(child: _signatureUploadedWidget(context)),
                      ],
                    ),
            ],
          ),
        );
      },
    );
  }

  Widget _signatureDrawingPad(
    BuildContext context,
    SignatureController _controller,
  ) {
    final isTablet = Responsive.isTablet(context);
    return Container(
      height: 180,
      padding: const EdgeInsets.symmetric(
        horizontal: defaultPadding,
        vertical: defaultPadding / 2,
      ),
      decoration: BoxDecoration(
        border: Border.all(color: AppTheme.signatureUploadBorderColor),
        borderRadius: BorderRadius.circular(25),
      ),
      child: Column(
        children: [
          Expanded(
            child: Container(
              child: Signature(
                controller: _controller,
                backgroundColor: Colors.white,
              ),
            ),
          ),
          const SizedBox(height: 8),
          Container(
            decoration: BoxDecoration(borderRadius: BorderRadius.circular(25)),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                _actionButton(
                  isTablet: isTablet,
                  label: AppStrings.clear,
                  onPressed: () => _controller.clear(),
                ),
                const SizedBox(width: 8),
                _actionButton(
                  isTablet: isTablet,
                  label: AppStrings.save,
                  onPressed: () async {
                    if (_controller.isNotEmpty) {
                      final signatureBytes = await _controller.toPngBytes();
                      if (signatureBytes != null) {
                        // TODO: Implement save logic
                      }
                    }
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _actionButton({
    required isTablet,
    required String label,
    required VoidCallback onPressed,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppTheme.signatureUploadBorderColor),
      ),
      child: TextButton(
        onPressed: onPressed,
        style: TextButton.styleFrom(
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        ),
        child: Padding(
          padding: EdgeInsets.symmetric(
            horizontal: isTablet ? defaultPadding / 2 : defaultPadding,
          ),
          child: Text(
            label,
            style: AppFonts.mediumTextStyle(
              isTablet ? 10 : 12,
              color: AppTheme.black,
            ),
          ),
        ),
      ),
    );
  }

  Widget _signatureUploadedWidget(BuildContext context) {
    final isTablet = Responsive.isTablet(context);
    return buildDottedBorderContainerWithRadius(
      borderColor: AppTheme.signatureUploadDottedColor,
      borderRadius: 25,
      child: ClipRRect(
        borderRadius: BorderRadiusGeometry.all(Radius.circular(25)),
        child: Container(
          height: 180,
          // padding: const EdgeInsets.symmetric(
          //   horizontal: defaultPadding,
          //   vertical: defaultPadding * 3,
          // ),
          color: AppTheme.docUploadBgColor,
          alignment: Alignment.center,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              GestureDetector(
                onTap: () async {
                  debugPrint('uploading');
                  // TODO: Implement file picker logic here
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 5,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    border: Border.all(
                      color: AppTheme.signatureUploadBorderColor,
                    ),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Image.asset(
                        '$iconAssetpath/upload.png',
                        height: 18,
                        width: 18,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        AppStrings.upload,
                        style: AppFonts.mediumTextStyle(
                          14,
                          color: AppTheme.black,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 18),
              Text(
                AppStrings.uploadText,
                textAlign: TextAlign.center,
                style: AppFonts.mediumTextStyle(
                  isTablet ? 12 : 14,
                  color: AppTheme.black,
                ),
              ),
              Text(
                AppStrings.uploadFormat,
                textAlign: TextAlign.center,
                style: AppFonts.regularTextStyle(12, color: AppTheme.black),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _docRepresentingRadio(ValueNotifier<bool?> isBuyer) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          representing,
          style: AppFonts.regularTextStyle(
            14,
            color: AppTheme.primaryTextColor,
          ),
        ),
        const SizedBox(width: defaultPadding),
        RadioOption<bool>(
          value: true,
          groupValue: isBuyer.value ?? false,
          onChanged: (value) => isBuyer.value = value,
          label: buyer,
          enabled: isEditable.value,
        ),
        const SizedBox(width: defaultPadding),
        RadioOption<bool>(
          value: false,
          groupValue: isBuyer.value ?? false,
          onChanged: (value) => isBuyer.value = value,
          label: seller,
          enabled: isEditable.value,
        ),
      ],
    );
  }

  Widget buildTextField(String label, String prefixIcon, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: AppFonts.regularTextStyle(
            14,
            color: AppTheme.primaryTextColor,
          ),
        ),
        const SizedBox(height: 6),
        TextField(
          controller: TextEditingController(text: value),
          style: AppFonts.regularTextStyle(
            14,
            color: AppTheme.primaryTextColor,
          ),
          enabled: isEditable.value,
          decoration: InputDecoration(
            prefixIcon: Container(
              margin: const EdgeInsets.only(left: 1, top: 1.5, bottom: 1.5),
              padding: const EdgeInsets.only(left: 2),
              decoration: BoxDecoration(
                color: AppTheme.white,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(55),
                  bottomLeft: Radius.circular(55),
                ),
              ),
              width: 40,
              child: Center(
                child: Image.asset(
                  '$iconAssetpath/$prefixIcon',
                  height: 18,
                  width: 18,
                ),
              ),
            ),
            filled: true,
            fillColor: AppTheme.formSubsectionBgColor,
            contentPadding: const EdgeInsets.symmetric(
              vertical: 10,
              horizontal: defaultPadding,
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(55),
              borderSide: BorderSide(color: AppTheme.textFieldBorder),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(55),
              borderSide: const BorderSide(color: AppTheme.textFieldBorder),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(55),
              borderSide: const BorderSide(
                color: AppTheme.primaryColor,
                width: 1,
              ),
            ),
            isDense: true,
          ),
        ),
      ],
    );
  }

  Widget buildTextFieldWithButtons(
    String label,
    String image,
    String value, {
    VoidCallback? onTap,
    String fileSize = '0 KB',
  }) {
    final isDocField =
        label == AppStrings.salesAgreement ||
        label == AppStrings.closingDocument;

    return GestureDetector(
      onTap: onTap,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: AppFonts.regularTextStyle(
              14,
              color: AppTheme.primaryTextColor,
            ),
          ),
          const SizedBox(height: 6),
          Container(
            padding: const EdgeInsets.symmetric(vertical: 5, horizontal: 18),
            decoration: BoxDecoration(
              color: isEditable.value
                  ? Colors.white
                  : AppTheme.formSubsectionBgColor,
              borderRadius: BorderRadius.circular(25),
              border: Border.all(color: Colors.grey.shade300),
            ),
            child: Row(
              children: [
                Image.asset('$iconAssetpath/$image', height: 18, width: 18),
                const SizedBox(width: 12),
                Expanded(
                  child: RichText(
                    strutStyle: const StrutStyle(
                      height: 1,
                      forceStrutHeight: true,
                    ),
                    text: TextSpan(
                      children: [
                        TextSpan(
                          text: value,
                          style: AppFonts.mediumTextStyle(
                            12,
                            color: AppTheme.primaryTextColor,
                          ),
                        ),
                        TextSpan(
                          text: '\n$fileSize',
                          style: AppFonts.regularTextStyle(
                            10,
                            color: Colors.grey,
                          ),
                        ),
                      ],
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                if (isDocField && isEditable.value) ..._buildDocActionButtons(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  List<Widget> _buildDocActionButtons() {
    return [
      GestureDetector(
        onTap: () {
          // TODO: Implement preview logic
        },
        child: Image.asset(
          '$iconAssetpath/preview.png',
          height: 24,
          width: 24,
          semanticLabel: AppStrings.view,
        ),
      ),
      const SizedBox(width: 8),
      GestureDetector(
        onTap: () {
          // TODO: Implement download logic
        },
        child: Image.asset(
          '$iconAssetpath/download.png',
          height: 24,
          width: 24,
          semanticLabel: AppStrings.download,
        ),
      ),
    ];
  }
}
