import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:neorevv/src/core/config/constants.dart';
import 'package:neorevv/src/core/config/responsive.dart';
import 'package:neorevv/src/core/config/json_consts.dart';
import 'package:neorevv/src/core/theme/app_fonts.dart';
import 'package:neorevv/src/core/theme/app_theme.dart';
import 'package:neorevv/src/domain/models/report.dart';
import 'components/report_sidebar.dart';
import 'components/pdf_preview_widget.dart';

class ReportScreen extends HookWidget {
  const ReportScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final selectedReport = useState<ReportModel?>(null);
    final reports = useState<List<ReportModel>>(reportsListData);

    // Debug: Print build method call and data verification
    debugPrint('ReportScreen: build() called, reports count: ${reports.value.length}');
    debugPrint('ReportScreen: reportsListData length: ${reportsListData.length}');
    if (reportsListData.isNotEmpty) {
      debugPrint('ReportScreen: First report in static data: ${reportsListData.first.title}');
    }

    // Set initial selected report
    useEffect(() {
      debugPrint('ReportScreen: useEffect called, reports count: ${reports.value.length}');
      if (reports.value.isNotEmpty) {
        try {
          selectedReport.value = reports.value.firstWhere((r) => r.isSelected);
          debugPrint('ReportScreen: Selected report from isSelected: ${selectedReport.value?.title}');
        } catch (e) {
          // If no report is selected, select the first one
          selectedReport.value = reports.value.first;
          debugPrint('ReportScreen: Selected first report: ${selectedReport.value?.title}');
        }
      } else {
        selectedReport.value = null;
        debugPrint('ReportScreen: No reports available');
      }
      return null;
    }, []);

    void onReportSelected(ReportModel report) {
      // Update selection state
      final updatedReports = reports.value.map((r) {
        return r.copyWith(isSelected: r.id == report.id);
      }).toList();

      reports.value = updatedReports;
      selectedReport.value = report;
    }

    Widget buildNoReportsAvailable() {
      return LayoutBuilder(
        builder: (context, constraints) {
          return Container(
            width: constraints.maxWidth,
            height: constraints.maxHeight,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withValues(alpha: 0.1),
              spreadRadius: 1,
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.folder_open_outlined,
                size: 80,
                color: Colors.grey[400],
              ),
              const SizedBox(height: 24),
              Text(
                'No Reports Available',
                style: AppFonts.semiBoldTextStyle(
                  24,
                  color: AppTheme.primaryTextColor,
                ),
              ),
              const SizedBox(height: 12),
              Text(
                'There are currently no reports to display.\nPlease check back later or contact your administrator.',
                textAlign: TextAlign.center,
                style: AppFonts.regularTextStyle(
                  16,
                  color: Colors.grey[600],
                ),
              ),
              const SizedBox(height: 32),
              ElevatedButton.icon(
                onPressed: () {
                  // You can add refresh functionality here
                  // For now, it just shows a message
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Refresh functionality can be implemented here'),
                      duration: Duration(seconds: 2),
                    ),
                  );
                },
                icon: const Icon(Icons.refresh),
                label: const Text('Refresh'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.primaryColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 12,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    },
    );
  }

    double getResponsiveSidebarWidth(double screenWidth) {
      if (screenWidth > 1200) {
        // Width > 1200: Responsive sidebar width
        return screenWidth > 1400 ? 280.0 : 260.0;
      } else {
        // Width <= 1200: Constant sidebar width
        return 300.0;
      }
    }

    Widget buildDropdownLayout(
      List<ReportModel> reports,
      ReportModel? selectedReport,
      Function(ReportModel) onReportSelected,
      double pdfPanelHeight,
    ) {

      return Column(
        children: [
          // Dropdown for reports (width <= 500)
          Container(
            width: double.infinity,
            height: 50, // Reduced height
            margin: const EdgeInsets.only(bottom: defaultPadding),
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: BorderRadius.circular(25),
              border: Border.all(color: Colors.red, width: 2), // Debug border for dropdown
            ),
            child: DropdownButton<String>(
              value: selectedReport?.id, // Use ID instead of object
              hint: Text(
                'Select Report',
                style: AppFonts.regularTextStyle(14, color: Colors.grey[600]!),
              ),
              isExpanded: true,
              underline: const SizedBox(), // Remove default underline
              icon: Icon(
                Icons.keyboard_arrow_down,
                color: Colors.grey[600],
              ),
              items: reports.map((report) {
                return DropdownMenuItem<String>(
                  value: report.id, // Use unique ID
                  child: Row(
                    children: [
                      Image.asset(
                        '$iconAssetpath/fi-rs-copy-alt.png',
                        width: 16,
                        height: 16,
                        color: AppTheme.black,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          report.title,
                          style: AppFonts.regularTextStyle(14, color: AppTheme.black),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                );
              }).toList(),
              onChanged: (String? reportId) {
                if (reportId != null) {
                  final report = reports.firstWhere((r) => r.id == reportId);
                  onReportSelected(report);
                }
              },
            ),
          ),

          // PDF viewer below dropdown
          Expanded(
            child: selectedReport != null
                ? PdfPreviewWidget(
                    key: ValueKey(selectedReport.id),
                    report: selectedReport,
                  )
                : Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.grey.withValues(alpha: 0.1),
                          spreadRadius: 1,
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: const Center(
                      child: Text('Select a report to preview'),
                    ),
                  ),
          ),
        ],
      );
    }

    Widget buildMobileLayout(
      List<ReportModel> reports,
      ReportModel? selectedReport,
      Function(ReportModel) onReportSelected,
      double sidebarHeight,
      double pdfPanelHeight,
    ) {
      final screenHeight = MediaQuery.of(context).size.height;
      final maxSidebarHeight = screenHeight * 0.4;
      debugPrint('MobileLayout: screenHeight=$screenHeight, sidebarHeight=$sidebarHeight, pdfPanelHeight=$pdfPanelHeight');
      debugPrint('MobileLayout: maxSidebarHeight=$maxSidebarHeight (40% of screen height)');
      debugPrint('MobileLayout: PDF panel will use Expanded widget without maxHeight constraint');

      return Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Sidebar at top for mobile - flexible height based on content
          Container(
            constraints: BoxConstraints(
              minHeight: 200,
              maxHeight: MediaQuery.of(context).size.height * 0.4, // Use 40% of screen height for better visibility
            ),
            padding: const EdgeInsets.all(defaultPadding),
            margin: const EdgeInsets.only(bottom: defaultPadding),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withValues(alpha: 0.1),
                  spreadRadius: 1,
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: ReportSidebar(
              reports: reports,
              onReportSelected: onReportSelected,
            ),
          ),

          // PDF viewer below sidebar
          Expanded(
            child: Container(
              constraints: const BoxConstraints(
                minHeight: 300, // Minimum height for PDF viewer
                // Remove maxHeight constraint to let Expanded handle sizing
              ),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withValues(alpha: 0.1),
                    spreadRadius: 1,
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: selectedReport != null
                  ? PdfPreviewWidget(
                      key: ValueKey(selectedReport.id),
                      report: selectedReport,
                    )
                  : Container(
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.grey.withValues(alpha: 0.1),
                            spreadRadius: 1,
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: const Center(
                        child: Text('Select a report to preview'),
                      ),
                    ),
            ),
          ),
        ],
      );
    }

    Widget buildDesktopLayout(
      List<ReportModel> reports,
      ReportModel? selectedReport,
      Function(ReportModel) onReportSelected,
      double sidebarWidth,
      double sidebarHeight,
      double pdfPanelHeight,
    ) {
      return Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Left Panel - Report Sidebar
          Container(
            width: sidebarWidth,
            height: sidebarHeight,
            padding: const EdgeInsets.all(defaultPadding),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              color: Colors.white,
            ),
            child: ReportSidebar(
              reports: reports,
              onReportSelected: onReportSelected,
            ),
          ),

          const SizedBox(width: defaultPadding),

          // Right Panel - PDF Preview
          Expanded(
            child: SizedBox(
              height: pdfPanelHeight,
              child: selectedReport != null
                  ? PdfPreviewWidget(
                      key: ValueKey(selectedReport.id),
                      report: selectedReport,
                    )
                  : Container(
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.grey.withValues(alpha: 0.1),
                            spreadRadius: 1,
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: const Center(
                        child: Text('Select a report to preview'),
                      ),
                    ),
            ),
          ),
        ],
      );
    }

    // Responsive sidebar width and layout
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final sidebarWidth = getResponsiveSidebarWidth(screenWidth);
    final sidebarHeight = screenHeight * 0.7; // 70% of screen height
    final isMobileLayout = Responsive.isMobile(context);
    final isDropdownLayout = Responsive.isSmallMobile(context);
    final pdfPanelHeight = screenHeight * 0.7; // 70% of screen height

    // Debug information
    debugPrint('ReportScreen: screenWidth=$screenWidth, isMobile=$isMobileLayout, isDropdown=$isDropdownLayout, reportsCount=${reports.value.length}');
    debugPrint('ReportScreen: mobileBreakpoint=$mobileBreakpoint, smallMobileBreakpoint=$smallMobileBreakpoint');
    debugPrint('ReportScreen: Expected behavior - ≤500: dropdown, 501-900: mobile (sidebar above), ≥901: desktop');
    if (reports.value.isNotEmpty) {
      debugPrint('ReportScreen: First report: ${reports.value.first.title}');
      debugPrint('ReportScreen: Selected report: ${selectedReport.value?.title ?? "None"}');
    }

    // Debug information for empty reports
    if (reports.value.isEmpty) {
      debugPrint('ReportScreen: No reports data available - this should not happen with static data');
    }

    return Builder(
      builder: (context) {
        return Column(
          mainAxisAlignment: MainAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            // Reports Heading - Responsive
            Container(
              padding: EdgeInsets.only(
                bottom: isDropdownLayout ? defaultPadding * 0.8 : defaultPadding,
                top: isDropdownLayout ? defaultPadding * 0.5 : 0,
              ),
              child: Row(
                children: [
                  Image.asset(
                    '$iconAssetpath/fi-rr-document.png',
                    width: isDropdownLayout ? 18 : 20,
                    height: isDropdownLayout ? 18 : 20,
                    color: AppTheme.black,
                  ),
                  const SizedBox(width: 8),
                  Flexible(
                    child: Text(
                      'Reports',
                      style: AppFonts.semiBoldTextStyle(
                        isDropdownLayout ? 20 : isMobileLayout ? 21 : 22,
                        color: AppTheme.black,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Main Content - Responsive Layout
            Flexible(
              fit: FlexFit.loose,
              child: reports.value.isEmpty
                  ? buildNoReportsAvailable()
                  : isDropdownLayout
                      ? ((){
                          debugPrint('ReportScreen: Using DROPDOWN layout');
                          return buildDropdownLayout(
                            reports.value,
                            selectedReport.value,
                            onReportSelected,
                            pdfPanelHeight,
                          );
                        })()
                      : isMobileLayout
                          ? ((){
                              debugPrint('ReportScreen: Using MOBILE layout');
                              return buildMobileLayout(
                                reports.value,
                                selectedReport.value,
                                onReportSelected,
                                sidebarHeight,
                                pdfPanelHeight,
                              );
                            })()
                          : ((){
                              debugPrint('ReportScreen: Using DESKTOP layout');
                              return buildDesktopLayout(
                                reports.value,
                                selectedReport.value,
                                onReportSelected,
                                sidebarWidth,
                                sidebarHeight,
                                pdfPanelHeight,
                              );
                            })(),
            ),
          ],
        );
      },
    );
  }
}
