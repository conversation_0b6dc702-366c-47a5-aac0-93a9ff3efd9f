import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import '../../../core/config/app_strings.dart';
import '../../../core/theme/app_fonts.dart';
import '../../../core/config/constants.dart';
import '../../../domain/models/broker.dart';
import 'components/dashboard_content.dart';

import '../../../core/theme/app_theme.dart';

class DashboardScreen extends HookWidget {
  final Function(Broker)? onNavigateToAgentNetwork;

  const DashboardScreen({super.key, this.onNavigateToAgentNetwork});

  @override
  Widget build(BuildContext context) {
    return Builder(
      builder: (context) {
        return Column(
          mainAxisAlignment: MainAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            _welcomeUser(),
            const SizedBox(height: defaultPadding / 2),
            DashboardContent(
              onNavigateToAgentNetwork: onNavigateToAgentNetwork,
            ),
            const SizedBox(height: defaultPadding),
          ],
        );
      },
    );
  }

  Widget _welcomeUser() {
    return Align(
      alignment: Alignment.centerLeft,
      child: RichText(
        textAlign: TextAlign.start,
        text: TextSpan(
          text: welcomeLabel,
          style: AppFonts.regularTextStyle(
            22,
            color: AppTheme.primaryTextColor.withValues(alpha: 0.7),
          ),
          children: [
            TextSpan(
              text: 'Nabil',
              style: AppFonts.semiBoldTextStyle(
                22,
                color: AppTheme.primaryTextColor,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
