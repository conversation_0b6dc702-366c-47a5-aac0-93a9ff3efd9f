import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:neorevv/src/core/utils/validators.dart';

import '../../../core/theme/app_fonts.dart';
import '../../../core/theme/app_theme.dart';

class AppTextField extends StatelessWidget {
  final TextEditingController controller;
  final String hintText;
  final bool isObscure;
  final String? errorText;
  final bool showToggle;
  final VoidCallback? onToggleObscure;
  final FocusNode? focusNode;
  final TextInputType? keyboardType;
  final String? Function(String?)? validator;
  final bool isMobile;
  final bool isMandatory;
  final List<TextInputFormatter>? inputFormatters;

  const AppTextField({
    Key? key,
    required this.controller,
    required this.hintText,
    this.isObscure = false,
    this.showToggle = false,
    this.onToggleObscure,
    this.focusNode,
    this.errorText,
    this.keyboardType = TextInputType.text,
    this.validator,
    this.inputFormatters,
    required this.isMobile,
    this.isMandatory = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      controller: controller,
      focusNode: focusNode,
      obscureText: isObscure,
      keyboardType: keyboardType,
       inputFormatters: inputFormatters,
      validator:
          validator ??
          (isMandatory
              ? (value) => InputValidators.validateRequiredField(value)
              : null),
      autovalidateMode: AutovalidateMode.onUserInteraction,
      style: AppFonts.regularTextStyle(
        isMobile ? 16 : 14,
        color: AppTheme.primaryTextColor,
      ),
      decoration: InputDecoration(
        hintText: hintText,
        hintStyle: AppFonts.regularTextStyle(
          isMobile ? 16 : 14,
          color: AppTheme.textFieldHint,
        ),
        errorText: errorText,
        suffixIcon: showToggle
            ? IconButton(
                icon: Icon(
                  isObscure ? Icons.visibility_off : Icons.visibility,
                  color: Colors.grey,
                  size: 20,
                ),
                onPressed: onToggleObscure,
              )
            : null,
        filled: true,
        fillColor: Colors.grey.shade50,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(25),
          borderSide: BorderSide(color: AppTheme.textFieldBorder),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(25),
          borderSide: BorderSide(
            color: errorText != null ? Colors.red : AppTheme.textFieldBorder,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(25),
          borderSide: BorderSide(
            color: errorText != null ? Colors.red : AppTheme.primaryColor,
            width: 2,
          ),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(25),
          borderSide: const BorderSide(color: Colors.red, width: 1),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(25),
          borderSide: const BorderSide(color: Colors.red, width: 2),
        ),
        contentPadding: EdgeInsets.symmetric(
          horizontal: 16,
          vertical: isMobile ? 18 : 16,
        ),
      ),
    );
  }
}
